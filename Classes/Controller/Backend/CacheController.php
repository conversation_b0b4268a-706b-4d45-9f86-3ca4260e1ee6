<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Controller\Backend;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Core\Cache\CacheManager;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Http\JsonResponse;
use TYPO3\CMS\Core\Http\RedirectResponse;
use TYPO3\CMS\Core\Messaging\FlashMessage;
use TYPO3\CMS\Core\Messaging\FlashMessageService;
use TYPO3\CMS\Core\Type\ContextualFeedbackSeverity;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Backend controller for cache operations
 */
class CacheController
{
    protected CacheManager $cacheManager;
    protected ConnectionPool $connectionPool;
    protected UriBuilder $uriBuilder;
    protected FlashMessageService $flashMessageService;

    public function __construct()
    {
        $this->cacheManager = GeneralUtility::makeInstance(CacheManager::class);
        $this->connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $this->uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);
        $this->flashMessageService = GeneralUtility::makeInstance(FlashMessageService::class);
    }

    /**
     * Clear frontend cache for all template pages
     */
    public function clearTemplateCacheAction(ServerRequestInterface $request): ResponseInterface
    {
        $queryParams = $request->getQueryParams();
        $landingPageId = (int)($queryParams['landingPageId'] ?? 0);

        if ($landingPageId === 0) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid landing page ID']);
        }

        try {
            // Get all template page UIDs related to this landing page
            $templatePageUids = $this->getTemplatePageUids($landingPageId);

            if (empty($templatePageUids)) {
                return new JsonResponse(['success' => false, 'message' => 'No template pages found']);
            }

            // Clear frontend cache for all template pages
            $this->clearFrontendCacheForPages($templatePageUids);

            // Add flash message for success
            $this->addFlashMessage(
                sprintf('Frontend cache cleared for %d template page(s)', count($templatePageUids)),
                'Cache Cleared',
                ContextualFeedbackSeverity::OK
            );

            // Redirect back to the landing page
            $redirectUri = $this->uriBuilder->buildUriFromRoute('web_layout', [
                'id' => $landingPageId
            ]);

            return new RedirectResponse($redirectUri);

        } catch (\Exception $e) {
            // Add flash message for error
            $this->addFlashMessage(
                'Failed to clear cache: ' . $e->getMessage(),
                'Cache Clear Failed',
                ContextualFeedbackSeverity::ERROR
            );

            // Redirect back to the landing page
            $redirectUri = $this->uriBuilder->buildUriFromRoute('web_layout', [
                'id' => $landingPageId
            ]);

            return new RedirectResponse($redirectUri);
        }
    }

    /**
     * Get all template page UIDs related to a landing page
     */
    protected function getTemplatePageUids(int $landingPageId): array
    {
        $templatePageUids = [];

        // Get the main template page from the landing page
        $queryBuilder = $this->connectionPool->getQueryBuilderForTable('pages');
        $landingPage = $queryBuilder
            ->select('tx_landingpages_template_page')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($landingPageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        if ($landingPage && !empty($landingPage['tx_landingpages_template_page'])) {
            $templatePageUids[] = (int)$landingPage['tx_landingpages_template_page'];
        }

        // Get template pages from template mappings
        $queryBuilder = $this->connectionPool->getQueryBuilderForTable('tx_landingpages_domain_model_templatemapping');
        $templateMappings = $queryBuilder
            ->select('template_page_uid')
            ->from('tx_landingpages_domain_model_templatemapping')
            ->where(
                $queryBuilder->expr()->eq('landing_page_uid', $queryBuilder->createNamedParameter($landingPageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->gt('template_page_uid', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAllAssociative();

        foreach ($templateMappings as $mapping) {
            $templatePageUid = (int)$mapping['template_page_uid'];
            if ($templatePageUid > 0 && !in_array($templatePageUid, $templatePageUids)) {
                $templatePageUids[] = $templatePageUid;
            }
        }

        return $templatePageUids;
    }

    /**
     * Clear frontend cache for specific pages
     */
    protected function clearFrontendCacheForPages(array $pageUids): void
    {
        $pageCache = $this->cacheManager->getCache('pages');
        
        foreach ($pageUids as $pageUid) {
            // Clear cache by page ID tag
            $pageCache->flushByTag('pageId_' . $pageUid);
        }

        // Also clear the general hash cache which might contain related data
        $hashCache = $this->cacheManager->getCache('hash');
        $hashCache->flush();
    }

    /**
     * Add a flash message
     */
    protected function addFlashMessage(string $message, string $title, ContextualFeedbackSeverity $severity): void
    {
        $flashMessage = GeneralUtility::makeInstance(
            FlashMessage::class,
            $message,
            $title,
            $severity,
            true
        );

        $messageQueue = $this->flashMessageService->getMessageQueueByIdentifier();
        $messageQueue->addMessage($flashMessage);
    }
}
