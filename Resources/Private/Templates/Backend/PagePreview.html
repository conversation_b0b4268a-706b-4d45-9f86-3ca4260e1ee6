<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:core="http://typo3.org/ns/TYPO3/CMS/Core/ViewHelpers"
      data-namespace-typo3-fluid="true">

<div class="flight-landing-page-preview">
    <div class="alert alert-info">
        <div class="media">
            <div class="media-left">
                <core:icon identifier="apps-pagetree-flight-template" size="small" />
            </div>
            <div class="media-body">
                <h5 class="mt-0 mb-3">
                    <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.template_pages_title" />
                </h5>
                <div class="template-pages-list">
                    <!-- Default Template -->
                    <div class="template-page-item mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge badge-primary mr-2">Default</span>
                            <f:if condition="{isTemplatePageHidden}">
                                <f:then>
                                    <span class="text-muted">
                                        <core:icon identifier="actions-edit-hide" size="small" />
                                        {templatePage.title} (UID: {templatePage.uid})
                                    </span>
                                    <span class="badge badge-warning ml-2">Hidden</span>
                                </f:then>
                                <f:else>
                                    <span class="text-success">
                                        <core:icon identifier="actions-check" size="small" />
                                        {templatePage.title} (UID: {templatePage.uid})
                                    </span>
                                </f:else>
                            </f:if>
                            <f:if condition="{isTemplatePageTemplate}">
                                <f:else>
                                    <span class="badge badge-warning ml-2">
                                        <core:icon identifier="actions-exclamation" size="small" />
                                        Invalid Type
                                    </span>
                                </f:else>
                            </f:if>
                        </div>
                    </div>

                    <!-- Template Variants -->
                    <f:if condition="{templateMappings}">
                        <f:for each="{templateMappings}" as="mapping">
                            <div class="template-page-item mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="badge badge-info mr-2">
                                        {mapping.origin_type -> f:format.case(mode: 'capital')} → {mapping.destination_type -> f:format.case(mode: 'capital')}
                                    </span>
                                    <f:if condition="{mapping.template_page_uid} && {mapping.template_title}">
                                        <f:then>
                                            <f:if condition="{mapping.template_hidden}">
                                                <f:then>
                                                    <span class="text-muted">
                                                        <core:icon identifier="actions-edit-hide" size="small" />
                                                        {mapping.template_title} (UID: {mapping.template_page_uid})
                                                    </span>
                                                    <span class="badge badge-warning ml-2">Hidden</span>
                                                </f:then>
                                                <f:else>
                                                    <span class="text-success">
                                                        <core:icon identifier="actions-check" size="small" />
                                                        {mapping.template_title} (UID: {mapping.template_page_uid})
                                                    </span>
                                                </f:else>
                                            </f:if>
                                        </f:then>
                                        <f:else>
                                            <span class="text-muted">
                                                <core:icon identifier="actions-exclamation" size="small" />
                                                No template page assigned
                                            </span>
                                        </f:else>
                                    </f:if>
                                </div>
                            </div>
                        </f:for>
                    </f:if>
                </div>
            </div>
        </div>
    </div>

    <!-- Flight Routes Section -->
    <f:if condition="{routeCount} > 0">
        <f:then>
            <div class="alert alert-info mt-3">
                <div class="media">
                    <div class="media-left">
                        <core:icon identifier="apps-pagetree-flight-landing" size="small" />
                    </div>
                    <div class="media-body">
                        <div class="d-flex align-items-center mb-2">
                            <h5 class="mt-0 mb-0">
                                <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.destination_pairs_title" />
                                <span class="badge badge-secondary">{routeCount}</span>
                                <f:if condition="{inactiveRouteCount} > 0">
                                    <span class="badge badge-success">{activeRouteCount} <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.active_count" /></span>
                                    <span class="badge badge-warning">{inactiveRouteCount} <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.inactive_count" /></span>
                                </f:if>
                            </h5>
                            <div class="flex-grow-1"></div>
                            <div class="backend-search-input mr-3">
                                <div class="input-group input-group-sm">
                                    <input type="text" id="backend-route-search" class="form-control form-control-sm" placeholder="Search...">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" id="clear-backend-search" title="Clear search" style="display: none;">×</button>
                                    </div>
                                </div>
                            </div>
                            <div class="btn-group" role="group">
                                <f:if condition="{csvImportUrl}">
                                    <a href="{csvImportUrl}" class="btn btn-sm btn-default" title="{f:translate(key: 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.import_csv_title')}">
                                        <core:icon identifier="actions-upload" size="small" />
                                        <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.import_csv_button" />
                                    </a>
                                </f:if>
                                <f:if condition="{csvExportUrl}">
                                    <a href="{csvExportUrl}" class="btn btn-sm btn-default" title="{f:translate(key: 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.export_csv_title')}">
                                        <core:icon identifier="actions-download" size="small" />
                                        <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.export_csv_button" />
                                    </a>
                                </f:if>
                                <f:if condition="{addRouteUrl}">
                                    <a href="{addRouteUrl}" class="btn btn-sm btn-primary" title="{f:translate(key: 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.add_destination_pair')}">
                                        <core:icon identifier="actions-add" size="small" />
                                        <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.add_pairs_button" />
                                    </a>
                                </f:if>
                            </div>
                        </div>
                        <small class="text-muted mb-2 d-block">
                            <span id="backend-results-count"></span>
                        </small>

                        <div class="flight-routes-list mt-3">
                            <f:for each="{flightRoutes}" as="route" iteration="iterator">
                                <div class="flight-route-item{f:if(condition: '{route.isActive}', else: ' route-inactive')}"
                                     data-search-content="{route.originCode} {route.destinationCode} {route.originName} {route.destinationName} {route.routeSlug}"
                                     data-route-active="{route.isActive}">
                                    <div class="route-actions">
                                        <div class="btn-group" role="group">
                                            <f:if condition="{route.previewUrl}">
                                                <f:if condition="{route.isActive}">
                                                    <f:then>
                                                        <a href="{route.previewUrl}" target="_blank" title="{f:translate(key: 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.preview_route')}" class="btn btn-default btn-sm">
                                                            <core:icon identifier="actions-view" size="small" />
                                                        </a>
                                                    </f:then>
                                                    <f:else>
                                                        <span class="btn btn-default btn-sm disabled" title="{f:translate(key: 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.preview_route_inactive')}">
                                                            <core:icon identifier="actions-view" size="small" />
                                                        </span>
                                                    </f:else>
                                                </f:if>
                                            </f:if>
                                            <f:if condition="{route.editUrl}">
                                                <a href="{route.editUrl}" title="{f:translate(key: 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.edit_route')}" class="btn btn-default btn-sm">
                                                    <core:icon identifier="actions-open" size="small" />
                                                </a>
                                            </f:if>
                                        </div>
                                    </div>
                                    <div class="route-codes">
                                        <span class="badge badge-{route.originType}-origin">
                                            {route.originCode}
                                        </span>
                                        <span class="route-arrow">
                                            <core:icon identifier="actions-move-right" size="small" />
                                        </span>
                                        <span class="badge badge-{route.destinationType}-destination">
                                            {route.destinationCode}
                                        </span>
                                    </div>
                                    <div class="route-names">
                                        <span class="text-muted">
                                            {route.originName} → {route.destinationName}
                                        </span>
                                        <f:if condition="{route.isActive}">
                                            <f:else>
                                                <span class="badge badge-warning ml-2">
                                                    <core:icon identifier="actions-edit-hide" size="small" />
                                                    <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.route_inactive" />
                                                </span>
                                            </f:else>
                                        </f:if>
                                    </div>
                                    <f:if condition="{route.routeSlug}">
                                        <div class="route-slug">
                                            <small class="text-muted">
                                                <core:icon identifier="actions-link" size="small" />
                                                <code>/{route.routeSlug}</code>
                                            </small>
                                        </div>
                                    </f:if>
                                </div>
                            </f:for>
                        </div>
                    </div>
                </div>
            </div>
        </f:then>
        <f:else>
            <div class="alert alert-warning mt-3">
                <div class="media">
                    <div class="media-left">
                        <core:icon identifier="actions-exclamation" size="small" />
                    </div>
                    <div class="media-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mt-0 mb-2">
                                <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.no_destination_pairs_title" />
                            </h5>
                            <f:if condition="{addRouteUrl}">
                                <a href="{addRouteUrl}" class="btn btn-sm btn-success" title="{f:translate(key: 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.add_destination_pair')}">
                                    <core:icon identifier="actions-add" size="small" />
                                    <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.add_first_pair_button" />
                                </a>
                            </f:if>
                        </div>
                        <p class="mb-0">
                            <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.preview.no_destination_pairs_message" />
                        </p>
                    </div>
                </div>
            </div>
        </f:else>
    </f:if>
</div>

</html>
